#!/bin/bash
# run-platform-tests.sh - Enhanced version with retry logic and crash recovery

# Source .env file if it exists
if [ -f ".env" ]; then
    source .env
fi

platform=$1
option=$2
start_num=$3
end_num=$4

# Color codes
RED='\033[31m'
GREEN='\033[32m'
YELLOW='\033[33m'
BLUE='\033[34m'
MAGENTA='\033[35m'
CYAN='\033[36m'
WHITE='\033[37m'
BOLD='\033[1m'
RESET='\033[0m'

# ===== Performance Optimization Settings =====
export NODE_OPTIONS="${NODE_OPTIONS:---max-old-space-size=4096}"
export APPIUM_TIMEOUT="${APPIUM_TIMEOUT:-60000}"
export IMPLICIT_WAIT="${IMPLICIT_WAIT:-10000}"
export PAGE_LOAD_TIMEOUT="${PAGE_LOAD_TIMEOUT:-30000}"

# ===== Test Execution Settings =====
RESET_INTERVAL="${RESET_INTERVAL:-9999999999}"  # Reset app after N tests
MAX_RETRY_ATTEMPTS="${MAX_RETRY_ATTEMPTS:-2}" # default is 2 - run 2 times including the first run
DEEP_CLEANUP_INTERVAL="${DEEP_CLEANUP_INTERVAL:-9999999999}"  # Deep cleanup after N tests
CRASH_RECOVERY_ENABLED="${CRASH_RECOVERY_ENABLED:-true}"  # Enable crash recovery
MAX_CRASH_RETRIES="${MAX_CRASH_RETRIES:-3}"  # Maximum number of retries when crashing

echo -e "${BLUE}🚀 Performance optimization enabled:${RESET}"
echo -e "${CYAN} - Node memory: $(echo $NODE_OPTIONS | grep -o '[0-9]\+' | awk '{print $1/1024}')GB${RESET}"
echo -e "${CYAN} - App reset interval: every ${RESET_INTERVAL} tests${RESET}"
echo -e "${CYAN} - Deep cleanup interval: every ${DEEP_CLEANUP_INTERVAL} tests${RESET}"
echo -e "${CYAN} - Retry failed tests: enabled${RESET}"
echo -e "${CYAN} - Crash recovery: ${CRASH_RECOVERY_ENABLED}${RESET}"

# ===== Slack Configuration =====
SLACK_WEBHOOK="${SLACK_WEBHOOK:-}"

if [ -z "$SLACK_WEBHOOK" ]; then
    echo -e "${YELLOW}⚠️ SLACK_WEBHOOK environment variable not set - Slack notifications will be disabled${RESET}"
fi

# ===== S3 Configuration =====
S3_BUCKET="dev-kcmsr-universal-link"
S3_BASE_PATH="e2e-test-report"
AWS_PROFILE="guide-s3-sync-user"
REPORT_DOMAIN="universal-link.kcmsr.dev.guide.inc"

# ===== Script Execution Context Detection =====
should_upload_to_s3=false
remote_testkit=false

# Check if "s3" parameter is provided in any position
for arg in "$@"; do
    if [ "$arg" = "s3" ]; then
        should_upload_to_s3=true
        echo -e "${BLUE}📊 S3 parameter detected - S3 upload will be performed${RESET}"
        break
    fi
done

if [ "$should_upload_to_s3" = false ]; then
    echo -e "${YELLOW}📝 No S3 parameter - S3 upload will be skipped${RESET}"
fi

# Check if "remote" parameter is provided in any position
for arg in "$@"; do
    if [ "$arg" = "remote" ]; then
        remote_testkit=true
        echo -e "${BLUE}🌐 Remote parameter detected - Remote TestKit will be used${RESET}"
        break
    fi
done

if [ "$remote_testkit" = false ]; then
    echo -e "${YELLOW}📝 No remote parameter - Local TestKit will be used${RESET}"
fi

# Function to clean up previous test results**
cleanup_previous_results() {
    echo -e "\n${YELLOW}${BOLD}🧹 Cleaning up previous test results...${RESET}"
    
    # Clean allure-results folder
    if [ -d "allure-results" ]; then
        echo -e "${BLUE}🗑️  Removing old allure-results...${RESET}"
        rm -rf allure-results/*
        echo -e "${GREEN}✅ allure-results cleaned${RESET}"
    else
        echo -e "${BLUE}📁 Creating allure-results folder...${RESET}"
        mkdir -p allure-results
    fi
    
    # Clean allure-report folder
    if [ -d "allure-report" ]; then
        echo -e "${BLUE}🗑️  Removing old allure-report...${RESET}"
        rm -rf allure-report/*
        echo -e "${GREEN}✅ allure-report cleaned${RESET}"
    else
        echo -e "${BLUE}📁 Creating allure-report folder...${RESET}"
        mkdir -p allure-report
    fi
    
    # Clean any leftover log files
    if ls test_output_*.log* 1> /dev/null 2>&1; then
        echo -e "${BLUE}🗑️  Removing old log files...${RESET}"
        rm -f test_output_*.log*
        echo -e "${GREEN}✅ Log files cleaned${RESET}"
    fi
    
    echo -e "${GREEN}${BOLD}✅ Previous test results cleaned successfully${RESET}\n"
}

# Function to check AWS CLI and profile configuration
check_aws_setup() {
    echo -e "${BLUE}🔍 Checking AWS setup...${RESET}"
    
    if ! command -v aws &> /dev/null; then
        echo -e "${RED}❌ AWS CLI not found. Please install AWS CLI first.${RESET}"
        return 1
    fi
    
    if ! aws configure list-profiles | grep -q "$AWS_PROFILE"; then
        echo -e "${YELLOW}⚠️ AWS profile '$AWS_PROFILE' not found.${RESET}"
        return 1
    fi
    
    if ! aws sts get-caller-identity --profile "$AWS_PROFILE" &> /dev/null; then
        echo -e "${RED}❌ AWS credentials verification failed${RESET}"
        return 1
    fi
    
    echo -e "${GREEN}✅ AWS setup verified successfully${RESET}"
    return 0
}

# Function to upload Allure report to S3
upload_report_to_s3() {
    local platform=$1
    local date_folder=$(date +%Y%m%d)
    local os_folder=""
    if [ "$platform" = "ios" ]; then
        os_folder="ios"
    else
        os_folder="android"
    fi
    
    local s3_path="s3://${S3_BUCKET}/${S3_BASE_PATH}/${date_folder}/${os_folder}/"
    
    echo -e "\n${BLUE}${BOLD}📤 Uploading test report to S3...${RESET}"
    echo -e "${CYAN}📅 Date folder: ${date_folder}${RESET}"
    echo -e "${CYAN}📱 Platform: ${os_folder}${RESET}"
    echo -e "${CYAN}🔗 S3 destination: ${s3_path}${RESET}"
    
    if [ ! -d "allure-report" ] || [ ! "$(ls -A allure-report)" ]; then
        echo -e "${YELLOW}⚠️ Generating Allure report...${RESET}"
        allure generate allure-results --clean -o allure-report
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ Failed to generate Allure report${RESET}"
            return 1
        fi
    fi
    
    echo -e "${YELLOW}📤 Syncing files to S3...${RESET}"
    aws s3 sync ./allure-report "$s3_path" --profile "$AWS_PROFILE" --delete
    
    if [ $? -eq 0 ]; then
        local report_url="https://${REPORT_DOMAIN}/${S3_BASE_PATH}/${date_folder}/${os_folder}/index.html#"
        echo -e "${GREEN}✅ Report uploaded successfully!${RESET}"
        echo -e "${BLUE}🌐 Report URL: ${report_url}${RESET}"
        
        if command -v pbcopy &> /dev/null; then
            echo "$report_url" | pbcopy
            echo -e "${GREEN}📋 URL copied to clipboard${RESET}"
        fi
        
        # Send NOTIFICATION SLACK after upload s3 successfully 
        send_s3_success_notification "$platform" "$date_folder" "$report_url"
        return 0
    else
        echo -e "${RED}❌ Failed to upload report to S3${RESET}"
        return 1
    fi
}
# Send notification to slack after upload s3 successfully
send_s3_success_notification() {
    local platform=$1
    local date_folder=$2
    local report_url=$3
    
    # Check if SLACK_WEBHOOK is set
    if [ -z "$SLACK_WEBHOOK" ]; then
        echo -e "${YELLOW}⚠️ SLACK_WEBHOOK not set - skipping Slack notification${RESET}"
        return 0
    fi
    
    local total_files=$(echo "$original_test_files" | grep -c "[^[:space:]]" 2>/dev/null || echo "0")
    local final_failed_count=${#final_failed_tests[@]}
    local final_passed_count=$((total_files - final_failed_count))
    local first_run_failed_count=${#first_run_failed_tests[@]}
    local recovered_count=$((first_run_failed_count - final_failed_count))
    local crash_count=${#crash_recovery_tests[@]}
    
    local success_rate=0
    if [ $total_files -gt 0 ]; then
        success_rate=$((final_passed_count * 100 / total_files))
    fi
    
    local platform_upper=$(echo "$platform" | tr '[:lower:]' '[:upper:]')
    
    # Build message content
    local message="📊 *E2E Test Report Published Successfully*\n"
    message+="═══════════════════════════════════\n"
    message+="🗓️ Date: *${date_folder}*\n"
    message+="📱 Platform: *${platform_upper}*\n"
    message+="═══════════════════════════════════\n"
    message+="📈 *Final Results:*\n\n"
    message+="✅ Passed: *${final_passed_count}* tests\n\n"
    message+="❌ Failed: *${final_failed_count}* tests\n\n"
    message+="📊 Total: *${total_files}* tests\n\n"
    message+="🎯 Success Rate: *${success_rate}%*\n\n"
    
    if [ $recovered_count -gt 0 ]; then
        message+="   🔄 Recovered: *${recovered_count}* tests\n\n"
    fi
    if [ $crash_count -gt 0 ]; then
        message+="   💥 Crash Recovery: *${crash_count}* tests\n\n"
    fi
    
    message+="═══════════════════════════════════\n\n"
    message+="🌐 *View Full Report:* ${report_url}\n\n"
    message+="═══════════════════════════════════\n\n"
    
    # Add failed tests section if any
    if [ $final_failed_count -gt 0 ]; then
        message+="\n\n⚠️ *Failed Tests (after retry):*\n"
        for test in "${final_failed_tests[@]}"; do
            message+="   • ${test}\n"
        done
    fi
    
    # Add recovered tests section if any
    if [ $recovered_count -gt 0 ]; then
        echo -e "\n${GREEN}${BOLD}🎉 Recovered Tests (failed first run, passed on retry):${RESET}"
        local recovered_tests=()
        for test in "${first_run_failed_tests[@]}"; do
            local found=false
            for final_test in "${final_failed_tests[@]}"; do
                if [ "$test" = "$final_test" ]; then
                    found=true
                    break
                fi
            done
            if [ "$found" = false ]; then
                recovered_tests+=("$test")
            fi
        done
        
        for test in "${recovered_tests[@]}"; do
            message+="   • ${test}\n"
        done
    fi
    
    # Add crash recovery section if any
    if [ $crash_count -gt 0 ]; then
        message+="\n💥 *Crash Recovery Applied:*\n"
        for test in "${crash_recovery_tests[@]}"; do
            message+="   • ${test}\n"
        done
    fi
    
    local notification_color="#36a64f"  # Green color like Backlog
    
    echo -e "${GREEN}📤 Sending notification to Slack with report URL...${RESET}"
    
    # Send the notification with proper JSON formatting
    curl -X POST -H 'Content-type: application/json' \
        --data "$(cat << EOF
{
    "attachments": [
        {
            "color": "$notification_color",
            "text": "$message",
            "mrkdwn_in": ["text"],
            "footer": "E2E Test Runner with Retry & Crash Recovery",
            "ts": $(date +%s)
        }
    ]
}
EOF
)" "$SLACK_WEBHOOK"
    
    local curl_exit_code=$?
    if [ $curl_exit_code -eq 0 ]; then
        echo -e "${GREEN}✅ Slack notification sent successfully${RESET}"
    else
        echo -e "${RED}❌ Failed to send Slack notification (exit code: $curl_exit_code)${RESET}"
    fi
}

# Function to send Slack notification
send_slack_notification() {
    local test_name=$1
    local status=$2
    local error_message=$3
    
    # Check if SLACK_WEBHOOK is set
    if [ -z "$SLACK_WEBHOOK" ]; then
        echo -e "${YELLOW}⚠️ SLACK_WEBHOOK not set - skipping Slack notification${RESET}"
        return 0
    fi
    
    if [ "$status" = "passed" ]; then
        local emoji="✅"
        local color="#36a64f"
    elif [ "$status" = "info" ]; then
        local emoji="ℹ️"
        local color="#0088cc"
    else
        local emoji="❌"
        local color="#dc3545"
    fi
    
    local message=""
    if [ "$test_name" = "KC member site e2e test" ]; then
        message="$emoji *KC member site e2e test*"
    else
        message="$emoji Test: **${test_name}**"
    fi
    
    if [ "$status" != "info" ]; then
        message="$message\nStatus: **${status}**"
    fi
    if [ ! -z "$error_message" ]; then
        message="$message\nDetails: \`\`\`$error_message\`\`\`"
    fi
    
    curl -X POST -H 'Content-type: application/json' \
        --data "{
            \"attachments\": [
                {
                    \"color\": \"$color\",
                    \"text\": \"$message\",
                    \"footer\": \"Test Runner\",
                    \"ts\": $(date +%s)
                }
            ]
        }" "$SLACK_WEBHOOK"
}

# Clean up previous results before running tests
cleanup_previous_results

# Test Slack connection at startup
if [ "$should_upload_to_s3" = true ]; then
    echo "Testing Slack connection..."
    send_slack_notification "KC member site e2e test" "info" "🚀 Starting test execution on $platform platform with retry logic and crash recovery..."
fi

# ===== ARRAYS TO STORE TEST RESULTS =====
declare -a first_run_failed_tests=()
declare -a first_run_error_details=()
declare -a final_failed_tests=()
declare -a final_error_details=()
declare -a crash_recovery_tests=()

# Load environment variables based on platform
if [ "$platform" = "ios" ]; then
    xcrun simctl terminate booted inc.guide.kabuappNext.dev
    sleep 2
    xcrun simctl launch booted inc.guide.kabuappNext.dev
else
    adb shell am force-stop inc.guide.kabuappNext.dev
    sleep 2
    adb shell monkey -p inc.guide.kabuappNext.dev 1
fi

# Enhanced function to detect ALL possible crash patterns
detect_crash_from_log() {
    local log_file=$1
    
    if [ ! -f "$log_file" ]; then
        return 1  # No log file, no crash
    fi
        # ===== EXCLUDE SPECIFIC WARNING PATTERNS (NOT CRASHES) =====
    # Check for specific Android warning that should NOT trigger crash recovery
    if grep -q "Not implemented yet for script.*timeouts.*script.*0" "$log_file"; then
        echo -e "${YELLOW}⚠️ Android timeouts warning detected (not a crash) - skipping crash recovery${RESET}" >&2
        return 1  # Not a crash, just a warning
    fi

    # ===== WEBDRIVER CRASHES =====
    if grep -q "ERROR webdriver:" "$log_file" || \
       grep -q "ERROR.*webdriver" "$log_file" || \
       grep -q "WebDriverError:" "$log_file" || \
       grep -q "WebDriverError.*Method is not implemented" "$log_file" || \
       grep -q "WebDriverError.*Invalid locator" "$log_file" || \
       grep -q "WebDriverError.*Timeout" "$log_file" || \
       grep -q "WebDriverError.*Connection refused" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== SESSION CRASHES =====
    if grep -q "session is either terminated or not started" "$log_file" || \
       grep -q "WebDriverError.*session.*terminated" "$log_file" || \
       grep -q "WebDriverError: A session is either terminated" "$log_file" || \
       grep -q "Session.*not found" "$log_file" || \
       grep -q "Invalid session ID" "$log_file" || \
       grep -q "Session ID.*does not exist" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== NETWORK/CONNECTION CRASHES =====
    if grep -q "ECONNREFUSED" "$log_file" || \
       grep -q "ECONNRESET" "$log_file" || \
       grep -q "ETIMEDOUT" "$log_file" || \
       grep -q "ENETUNREACH" "$log_file" || \
       grep -q "EHOSTUNREACH" "$log_file" || \
       grep -q "Request failed with error code" "$log_file" || \
       grep -q "Connection lost" "$log_file" || \
       grep -q "connect ECONNREFUSED" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== APPIUM SERVER CRASHES =====
    if grep -q "An unknown server-side error occurred" "$log_file" || \
       grep -q "Could not find a connected Android device" "$log_file" || \
       grep -q "Appium.*not running" "$log_file" || \
       grep -q "Unable to create session" "$log_file" || \
       grep -q "Appium server.*stopped" "$log_file" || \
       grep -q "Could not start Appium session" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== DEVICE/SIMULATOR CRASHES =====
    if grep -q "Device.*disconnected" "$log_file" || \
       grep -q "Simulator.*shutdown" "$log_file" || \
       grep -q "Device not found" "$log_file" || \
       grep -q "Simulator not found" "$log_file" || \
       grep -q "xcrun.*error" "$log_file" || \
       grep -q "adb.*device.*offline" "$log_file" || \
       grep -q "adb.*device not found" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== APP CRASHES =====
    if grep -q "Application.*crashed" "$log_file" || \
       grep -q "App.*terminated unexpectedly" "$log_file" || \
       grep -q "Process.*died" "$log_file" || \
       grep -q "Application not responding" "$log_file" || \
       grep -q "App.*not found" "$log_file" || \
       grep -q "Unable to launch app" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== TIMEOUT CRASHES (thường gặp) =====
    if grep -q "TimeoutError" "$log_file" || \
       grep -q "Timeout.*exceeded" "$log_file" || \
       grep -q "Command timeout" "$log_file" || \
       grep -q "Script timeout" "$log_file" || \
       grep -q "Page load timeout" "$log_file" || \
       grep -q "Element.*timeout" "$log_file" || \
       grep -q "waiting for element.*timeout" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== ELEMENT/LOCATOR CRASHES (phổ biến) =====
    if grep -q "StaleElementReferenceError" "$log_file" || \
       grep -q "NoSuchElementException" "$log_file" || \
       grep -q "ElementNotVisibleException" "$log_file" || \
       grep -q "ElementNotInteractableException" "$log_file" || \
       grep -q "Element is not clickable" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== MEMORY/RESOURCE CRASHES =====
    if grep -q "Out of memory" "$log_file" || \
       grep -q "JavaScript heap out of memory" "$log_file" || \
       grep -q "FATAL ERROR" "$log_file" || \
       grep -q "Heap.*exceeded" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== CODECEPT/FRAMEWORK CRASHES =====
    if grep -q "CodeceptJS.*crashed" "$log_file" || \
       grep -q "Helper.*crashed" "$log_file" || \
       grep -q "Browser.*crashed" "$log_file" || \
       grep -q "Driver.*crashed" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== IOS SPECIFIC CRASHES =====
    if grep -q "iPhone Simulator.*crashed" "$log_file" || \
       grep -q "CoreSimulator.*error" "$log_file" || \
       grep -q "xcrun simctl.*error" "$log_file" || \
       grep -q "xcodebuild.*failed" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== ANDROID SPECIFIC CRASHES =====
    if grep -q "adb.*error" "$log_file" || \
       grep -q "UiAutomator.*crashed" "$log_file" || \
       grep -q "AndroidDriver.*error" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== PERMISSION/SECURITY CRASHES =====
    if grep -q "Permission denied" "$log_file" || \
       grep -q "Access denied" "$log_file" || \
       grep -q "Unauthorized" "$log_file" || \
       grep -q "Security.*error" "$log_file"; then
        return 0  # Crash detected
    fi
    
    # ===== GENERIC FATAL ERRORS =====
    if grep -q "FATAL:" "$log_file" || \
       grep -q "CRITICAL:" "$log_file" || \
       grep -q "Segmentation fault" "$log_file" || \
       grep -q "Core dumped" "$log_file" || \
       grep -q "Abort trap" "$log_file"; then
        return 0  # Crash detected
    fi
    
    return 1  # No crash detected
}

# Function to recover from crash
recover_from_crash() {
    local platform=$1
    local current_file=$2
    local attempt=$3
    
    echo -e "\n${RED}${BOLD}💥 CRASH DETECTED - Attempt ${attempt}/${MAX_CRASH_RETRIES}${RESET}"
    echo -e "${YELLOW}${BOLD}🔧 Initiating crash recovery for $(basename $current_file)...${RESET}"
    
    # Perform comprehensive recovery
    if [ "$platform" = "ios" ]; then
        echo -e "${BLUE}🔄 iOS Crash Recovery:${RESET}"
        
        # Kill and restart simulator if needed
        xcrun simctl shutdown booted 2>/dev/null || true
        sleep 3
        xcrun simctl boot "iPhone 14 Pro" 2>/dev/null || xcrun simctl boot booted
        sleep 5
        
        # Terminate and restart app
        xcrun simctl terminate booted inc.guide.kabuappNext.dev 2>/dev/null || true
        sleep 2
        xcrun simctl launch booted inc.guide.kabuappNext.dev
        sleep 3
    else
        echo -e "${BLUE}🔄 Android Crash Recovery:${RESET}"
        
        adb shell am force-stop inc.guide.kabuappNext.dev
        sleep 1
        adb shell monkey -p inc.guide.kabuappNext.dev -c android.intent.category.LAUNCHER 1
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.CAMERA
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.ACCESS_FINE_LOCATION
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.READ_EXTERNAL_STORAGE
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.WRITE_EXTERNAL_STORAGE
        sleep 1
    fi
    
    # Re-run auto login after crash recovery
    echo -e "${YELLOW}🔑 Re-running auto login after crash recovery...${RESET}"
    cp .env.example .env
    sed -i '' "s|TEST_PATH_PATTERN=.*|TEST_PATH_PATTERN=./tests/common/autoLogin.ts|g" .env
    
    if [ "$platform" = "ios" ]; then
        FORCE_COLOR=1 npm run test-ios &>/dev/null
    else
        FORCE_COLOR=1 npm run test-android &>/dev/null
    fi
    
    sleep 2
    echo -e "${GREEN}✅ Crash recovery completed${RESET}"
}

# Function to extract error details from log
extract_error_details() {
    local file=$1
    local failed_array_name=$2
    local error_array_name=$3
    local log_suffix=$4
    
    local log_file="test_output_$(basename $file .ts)${log_suffix}.log"
    
    if [ -f "$log_file" ]; then
        local failures_section=$(awk '/-- FAILURES:/,/^o File:/' "$log_file" 2>/dev/null)
        
        if [ ! -z "$failures_section" ]; then
            eval "${failed_array_name}+=(\"$(basename $file)\")"
            eval "${error_array_name}+=(\"$failures_section\")"
        fi
    fi
}

# Function to colorize test output
colorize_output() {
    local current_file=$1
    local file_name=$(basename "$current_file" .ts)

    while IFS= read -r line; do
        if [[ $line =~ .*OK.*in.*[0-9]+ms.* ]]; then
            echo -e "${GREEN}${BOLD}$line${RESET}"
        elif [[ $line == *"✖ FAILED in"* ]]; then
            # Add file name prefix for failed tests
            echo -e "${RED}${BOLD}[${file_name}] $line${RESET}"
        elif [[ $line == *"Test Item"* ]]; then
            echo -e "${MAGENTA}${BOLD}$line${RESET}"
        elif [[ $line == *"Scenario()"* ]]; then
            # Add file name prefix for Scenario
            echo -e "${CYAN}[${file_name}] $line${RESET}"
        elif [[ $line == *"Before()"* ]] || [[ $line == *"After()"* ]]; then
            echo -e "${YELLOW}$line${RESET}"
        elif [[ $line =~ ^[[:space:]]*I[[:space:]] ]]; then
            echo -e "${WHITE}$line${RESET}"
        elif [[ $line =~ ^\{.*x:.*y:.*\}$ ]]; then
            echo -e "${BLUE}$line${RESET}"
        elif [[ $line == *"deviceScreenInfo"* ]]; then
            echo -e "${BLUE}$line${RESET}"
        elif [[ $line == *"ERROR webdriver:"* ]]; then
            echo -e "${RED}${BOLD}💥 WEBDRIVER ERROR: $line${RESET}"
        elif [[ $line == *"ERROR"* ]] && [[ $line == *"session is either terminated"* ]]; then
            echo -e "${RED}${BOLD}💥 CRASH: $line${RESET}"
        else
            echo "$line"
        fi
    done
}

# Function to reset app
reset_app() {
    local platform=$1
    echo -e "\n${YELLOW}${BOLD}🔄 Resetting app and auto login...${RESET}"
    
    cp .env.example .env
    sed -i '' "s|TEST_PATH_PATTERN=.*|TEST_PATH_PATTERN=./tests/0.login_test.ts|g" .env
    
    echo -e "${BLUE}Running 0.login_test.ts (resetAppFixed + login)...${RESET}"
    
    if [ "$platform" = "ios" ]; then
        if npm run test-ios &>/dev/null; then
            echo -e "${GREEN}✓ App reset and login completed${RESET}"
        else
            echo -e "${RED}❌ App reset and login failed${RESET}"
            return 1
        fi
    else
        if npm run test-android &>/dev/null; then
            echo -e "${GREEN}✓ App reset and login completed${RESET}"
        else
            echo -e "${RED}❌ App reset and login failed${RESET}"
            return 1
        fi
    fi
    
    echo -e "${GREEN}${BOLD}✅ App reset and auto login completed${RESET}\n"
}

# Function for deep cleanup
deep_cleanup() {
    local platform=$1
    echo -e "\n${YELLOW}${BOLD}🧹 Performing deep cleanup...${RESET}"
    
    if [ "$platform" = "ios" ]; then
        echo -e "${BLUE}Deep cleaning iOS simulator...${RESET}"
        xcrun simctl terminate booted inc.guide.kabuappNext.dev 2>/dev/null || true
        sleep 2
        xcrun simctl privacy booted reset all inc.guide.kabuappNext.dev 2>/dev/null || true
        sleep 2
        xcrun simctl launch booted inc.guide.kabuappNext.dev
        sleep 3
    else
        echo -e "${BLUE}Deep cleaning Android device...${RESET}"
        adb shell am force-stop inc.guide.kabuappNext.dev
        sleep 1
        adb shell monkey -p inc.guide.kabuappNext.dev -c android.intent.category.LAUNCHER 1
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.CAMERA
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.ACCESS_FINE_LOCATION
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.READ_EXTERNAL_STORAGE
        sleep 1
        adb shell pm grant inc.guide.kabuappNext.dev android.permission.WRITE_EXTERNAL_STORAGE
        sleep 1
    fi
    echo -e "${GREEN}✅ Deep cleanup completed${RESET}"
}

# Enhanced function to run a single test with crash recovery
run_single_test_with_crash_recovery() {
    local file=$1
    local platform=$2
    local log_suffix=$3
    local crash_attempt=${4:-1}
    
    echo -e "\n${BLUE}${BOLD}=== Running test: $(basename $file) on $platform ===${RESET}"
    if [ $crash_attempt -gt 1 ]; then
        echo -e "${YELLOW}${BOLD}🔄 Crash recovery attempt: ${crash_attempt}/${MAX_CRASH_RETRIES}${RESET}"
    fi
    echo ""
    
    cp .env.example .env
    sed -i '' "s|TEST_PATH_PATTERN=.*|TEST_PATH_PATTERN=$file|g" .env
    
    local log_file="test_output_$(basename $file .ts)${log_suffix}.log"
    
    if [ "$platform" = "ios" ]; then
        FORCE_COLOR=1 npm run test-ios 2>&1 | colorize_output "$file" | tee "$log_file"
        test_result=${PIPESTATUS[0]}
    else
        FORCE_COLOR=1 npm run test-android 2>&1 | colorize_output "$file" | tee "$log_file"
        test_result=${PIPESTATUS[0]}
    fi
    
    # Check for crash
    if detect_crash_from_log "$log_file"; then
        echo -e "\n${RED}${BOLD}💥 CRASH DETECTED in $(basename $file)${RESET}"
        
        if [ "$CRASH_RECOVERY_ENABLED" = true ] && [ $crash_attempt -lt $MAX_CRASH_RETRIES ]; then
            crash_recovery_tests+=("$(basename $file)")
            recover_from_crash "$platform" "$file" "$crash_attempt"
            
            # Retry the test after crash recovery
            local next_attempt=$((crash_attempt + 1))
            echo -e "${YELLOW}🔄 Retrying test after crash recovery...${RESET}"
            run_single_test_with_crash_recovery "$file" "$platform" "${log_suffix}_crash_retry_${crash_attempt}" "$next_attempt"
            return $?
        else
            echo -e "${RED}❌ Max crash recovery attempts reached for $(basename $file)${RESET}"
            return 1
        fi
    fi
    
    return $test_result
}

# Function to run a single test (wrapper)
run_single_test() {
    local file=$1
    local platform=$2
    local log_suffix=$3
    
    run_single_test_with_crash_recovery "$file" "$platform" "$log_suffix"
    return $?
}

# Function to display summary after first run
display_first_run_summary() {
    local total_files=$(echo "$original_test_files" | grep -c "[^[:space:]]")
    local failed_count=${#first_run_failed_tests[@]}
    local passed_count=$((total_files - failed_count))
    local crash_count=${#crash_recovery_tests[@]}
    
    echo -e "\n${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    echo -e "${MAGENTA}${BOLD}🎯 FIRST RUN SUMMARY${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    echo -e "${WHITE}📊 ${BOLD}First Run Results:${RESET}"
    echo -e "   ✅ Passed:      ${GREEN}${BOLD}${passed_count}${RESET} tests"
    echo -e "   ❌ Failed:      ${RED}${BOLD}${failed_count}${RESET} tests"
    echo -e "   📈 Total:       ${BLUE}${BOLD}${total_files}${RESET} tests"
    if [ $crash_count -gt 0 ]; then
        echo -e "   💥 Crash Recovery: ${YELLOW}${BOLD}${crash_count}${RESET} tests"
    fi
    
    if [ ${failed_count} -gt 0 ]; then
        echo -e "\n${RED}${BOLD}❌ Failed Tests (will be retried):${RESET}"
        for i in "${!first_run_failed_tests[@]}"; do
            echo -e "${RED}${BOLD}$((i + 1)). ${first_run_failed_tests[$i]}${RESET}"
        done
        echo -e "\n${YELLOW}${BOLD}🔄 Preparing to retry failed tests...${RESET}"
    else
        echo -e "\n${GREEN}${BOLD}🎉 ALL TESTS PASSED ON FIRST RUN!${RESET}"
    fi
    
    if [ $crash_count -gt 0 ]; then
        echo -e "\n💥 Tests with Crash Recovery:${RESET}"
        for i in "${!crash_recovery_tests[@]}"; do
            echo -e "${YELLOW}${BOLD}$((i + 1)). ${crash_recovery_tests[$i]}${RESET}"
        done
    fi
    
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
}

# Function to display final summary
display_final_summary() {
    local total_files=$(echo "$original_test_files" | grep -c "[^[:space:]]")
    local final_failed_count=${#final_failed_tests[@]}
    local final_passed_count=$((total_files - final_failed_count))
    local first_run_failed_count=${#first_run_failed_tests[@]}
    local recovered_count=$((first_run_failed_count - final_failed_count))
    local crash_count=${#crash_recovery_tests[@]}
    
    local success_rate=0
    if [ $total_files -gt 0 ]; then
        success_rate=$((final_passed_count * 100 / total_files))
    fi

    echo -e "\n${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    echo -e "${MAGENTA}${BOLD}🎯 FINAL TEST EXECUTION SUMMARY${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    echo -e "${WHITE}📊 ${BOLD}Final Results:${RESET}"
    echo -e "   ✅ Passed:      ${GREEN}${BOLD}${final_passed_count}${RESET} tests"
    echo -e "   ❌ Failed:      ${RED}${BOLD}${final_failed_count}${RESET} tests"
    echo -e "   📈 Total:       ${BLUE}${BOLD}${total_files}${RESET} tests"
    echo -e "   🎯 Success Rate: ${CYAN}${BOLD}${success_rate}%${RESET}"
    echo -e "   🔄 Recovered:   ${YELLOW}${BOLD}${recovered_count}${RESET} tests"
    if [ $crash_count -gt 0 ]; then
        echo -e "   💥 Crash Recovery: ${YELLOW}${BOLD}${crash_count}${RESET} tests"
    fi
    
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    if [ ${final_failed_count} -gt 0 ]; then
        echo -e "\n${RED}${BOLD}❌ Final Failed Tests (after retry):${RESET}"
        for i in "${!final_failed_tests[@]}"; do
            echo -e "${RED}${BOLD}$((i + 1)). ${final_failed_tests[$i]}${RESET}"
        done
        echo -e "\n${YELLOW}${BOLD}💡 These tests failed both runs - may need investigation${RESET}"
    fi
    
    if [ $recovered_count -gt 0 ]; then
        echo -e "\n${GREEN}${BOLD}🎉 Recovered Tests (failed first run, passed on retry):${RESET}"
        local recovered_tests=()
        for test in "${first_run_failed_tests[@]}"; do
            local found=false
            for final_test in "${final_failed_tests[@]}"; do
                if [ "$test" = "$final_test" ]; then
                    found=true
                    break
                fi
            done
            if [ "$found" = false ]; then
                recovered_tests+=("$test")
            fi
        done
        
        for i in "${!recovered_tests[@]}"; do
            echo -e "${GREEN}${BOLD}$((i + 1)). ${recovered_tests[$i]}${RESET}"
        done
    fi
    
    if [ $crash_count -gt 0 ]; then
        echo -e "\n💥 Tests Recovered from Crash:${RESET}"
        for i in "${!crash_recovery_tests[@]}"; do
            echo -e "${YELLOW}${BOLD}$((i + 1)). ${crash_recovery_tests[$i]}${RESET}"
        done
    fi
    
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    echo -e "${WHITE}🕐 Completed at: ${BOLD}$(date '+%Y-%m-%d %H:%M:%S')${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
}

# Get list of test files based on option
if [ "$option" = "range" ]; then
    if [ -z "$start_num" ] || [ -z "$end_num" ]; then
        echo -e "${RED}Error: Please provide start and end numbers for range option${RESET}"
        echo -e "${YELLOW}Usage: $0 <platform> range <start> <end>${RESET}"
        exit 1
    fi
    
    echo -e "${CYAN}${BOLD}Running tests from #${start_num} to #${end_num}${RESET}"
    original_test_files=$(find ./tests -name "*_test.ts" | perl -ne "if (/\/(\d+)\./ && \$1 >= $start_num && \$1 <= $end_num) {print \"\$1:\$_\"}" | sort -n | cut -d: -f2-)
    
# Run specific test group
elif [ "$option" = "group" ]; then
    echo -e "${CYAN}${BOLD}Running specific test group${RESET}"
    original_test_files=$(cat << EOF
        ./tests/138.hamburger_menu_test.ts
                ./tests/273.market_indicator_detail_test.ts

      
EOF
)
        #   ./tests/1790.card_confirm_test.ts
        #     ./tests/1810.card_common_unregister_test.ts
        #       ./tests/1863.change_withDrawal_password_test.ts

else
    echo -e "${RED}Error: Invalid option. Use 'range' or 'group'${RESET}"
    echo -e "${YELLOW}Usage: $0 <platform> <option> [start] [end]${RESET}"
    exit 1
fi

# Display execution order
echo -e "${CYAN}${BOLD}Files will be executed in this order:${RESET}"
echo "$original_test_files"
echo -e "${CYAN}${BOLD}===========================${RESET}"

# ===== FIRST RUN - ALL TESTS =====
echo -e "\n${MAGENTA}${BOLD}🚀 STARTING FIRST RUN - ALL TESTS${RESET}"
echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"

test_counter=0
first_run=true

for file in $original_test_files
do
    test_counter=$((test_counter + 1))
    
    echo -e "\n${MAGENTA}${BOLD}📊 Test Progress: ${test_counter}/$(echo "$original_test_files" | grep -c "[^[:space:]]") - $(basename $file)${RESET}"
    
    # Periodic app reset
    if [ $test_counter -gt 1 ] && [ $((test_counter % RESET_INTERVAL)) -eq 1 ]; then
        echo -e "\n${YELLOW}${BOLD}🔄 Periodic app reset (after $((test_counter - 1)) tests)${RESET}"
        reset_app "$platform"
    fi
    
    # Deep cleanup
    if [ $test_counter -gt 1 ] && [ $((test_counter % DEEP_CLEANUP_INTERVAL)) -eq 1 ]; then
        echo -e "\n${YELLOW}${BOLD}🧹 Performing deep cleanup (after $((test_counter - 1)) tests)${RESET}"
        deep_cleanup "$platform"
    fi
    
    # Auto login on first run
    if [ "$first_run" = true ]; then
        echo -e "\n${YELLOW}${BOLD}=== Running Auto Login ===${RESET}\n"
        
        cp .env.example .env
        sed -i '' "s|TEST_PATH_PATTERN=.*|TEST_PATH_PATTERN=./tests/common/autoLogin.ts|g" .env
        
        if [ "$platform" = "ios" ]; then
            FORCE_COLOR=1 npm run test-ios 2>&1 | colorize_output
        else
            FORCE_COLOR=1 npm run test-android 2>&1 | colorize_output
        fi
        
        first_run=false
        echo -e "\n${GREEN}${BOLD}✅ Auto Login Completed${RESET}\n"
        sleep 2
    fi
    
    # Run the test with crash recovery
    if run_single_test "$file" "$platform" "_first_run"; then
        echo -e "\n${GREEN}${BOLD}✅ Test passed for $(basename $file)${RESET}\n"
        # Keep log files for final cleanup decision
        # rm -f "test_output_$(basename $file .ts)_first_run.log"
        # rm -f "test_output_$(basename $file .ts)_first_run_crash_retry_"*.log
    else
        extract_error_details "$file" "first_run_failed_tests" "first_run_error_details" "_first_run"
        echo -e "\n${RED}${BOLD}❌ Test failed for $(basename $file)${RESET}\n"
    fi
    
    sleep 1
done

# Display first run summary
display_first_run_summary

# ===== RETRY LOGIC WITH MAX_RETRY_ATTEMPTS =====
if [ ${#first_run_failed_tests[@]} -gt 0 ]; then
    echo -e "\n${MAGENTA}${BOLD}🔄 STARTING RETRY RUNS - UP TO ${MAX_RETRY_ATTEMPTS} ATTEMPTS${RESET}"
    echo -e "${BLUE}${BOLD}═══════════════════════════════════════════${RESET}"
    
    # Copy first run failed tests to current retry list
    declare -a current_retry_tests=("${first_run_failed_tests[@]}")
    
    # Loop through retry attempts
    for attempt in $(seq 1 $MAX_RETRY_ATTEMPTS); do
        if [ ${#current_retry_tests[@]} -eq 0 ]; then
            echo -e "${GREEN}${BOLD}🎉 All tests recovered! No more retries needed.${RESET}"
            break
        fi
        
        echo -e "\n${YELLOW}${BOLD}🔄 RETRY ATTEMPT ${attempt}/${MAX_RETRY_ATTEMPTS}${RESET}"
        echo -e "${CYAN}Tests to retry: ${#current_retry_tests[@]}${RESET}"
        
        # Deep cleanup before each retry attempt
        echo -e "\n${YELLOW}${BOLD}🧹 Deep cleanup before retry attempt ${attempt}...${RESET}"
        deep_cleanup "$platform"
        
        # Re-run auto login for each retry attempt
        echo -e "\n${YELLOW}${BOLD}=== Re-running Auto Login for Retry Attempt ${attempt} ===${RESET}\n"
        cp .env.example .env
        sed -i '' "s|TEST_PATH_PATTERN=.*|TEST_PATH_PATTERN=./tests/common/autoLogin.ts|g" .env
        
        if [ "$platform" = "ios" ]; then
            FORCE_COLOR=1 npm run test-ios 2>&1 | colorize_output
        else
            FORCE_COLOR=1 npm run test-android 2>&1 | colorize_output
        fi
        
        echo -e "\n${GREEN}${BOLD}✅ Auto Login for Retry Attempt ${attempt} Completed${RESET}\n"
        sleep 2
        
        # Store tests that still fail in this attempt
        declare -a this_attempt_failed_tests=()
        
        # Retry each failed test in current list
        retry_counter=0
        for failed_test in "${current_retry_tests[@]}"; do
            retry_counter=$((retry_counter + 1))
            
            # Find the full path of the failed test
            failed_test_path=""
            for original_file in $original_test_files; do
                if [[ "$(basename $original_file)" == "$failed_test" ]]; then
                    failed_test_path="$original_file"
                    break
                fi
            done
            
            if [ -z "$failed_test_path" ]; then
                echo -e "${RED}❌ Could not find path for failed test: $failed_test${RESET}"
                continue
            fi
            
            echo -e "\n${YELLOW}${BOLD}🔄 Retry ${retry_counter}/${#current_retry_tests[@]} (Attempt ${attempt}): $(basename $failed_test_path)${RESET}"
            
            # Run the retry test with crash recovery
            if run_single_test "$failed_test_path" "$platform" "_retry_attempt_${attempt}"; then
                echo -e "\n${GREEN}${BOLD}✅ Test RECOVERED on retry attempt ${attempt}: $(basename $failed_test_path)${RESET}\n"
                # Keep log files for final cleanup decision
                # rm -f "test_output_$(basename $failed_test_path .ts)_retry_attempt_${attempt}.log"
                # rm -f "test_output_$(basename $failed_test_path .ts)_retry_attempt_${attempt}_crash_retry_"*.log
            else
                this_attempt_failed_tests+=("$failed_test")
                echo -e "\n${RED}${BOLD}❌ Test STILL FAILED on retry attempt ${attempt}: $(basename $failed_test_path)${RESET}\n"
            fi
            
            sleep 1
        done
        
        # Update current retry list for next attempt
        current_retry_tests=("${this_attempt_failed_tests[@]}")
        
        # Show progress after each attempt
        local recovered_this_attempt=$((${#current_retry_tests[@]} - ${#this_attempt_failed_tests[@]}))
        echo -e "\n${CYAN}${BOLD}📊 After Retry Attempt ${attempt}:${RESET}"
        echo -e "   ✅ Recovered: ${recovered_this_attempt} tests"
        echo -e "   ❌ Still failing: ${#this_attempt_failed_tests[@]} tests"
        
        # If no tests left to retry, break early
        if [ ${#this_attempt_failed_tests[@]} -eq 0 ]; then
            echo -e "${GREEN}${BOLD}🎉 All tests recovered after ${attempt} retry attempts!${RESET}"
            break
        fi
    done
    
    # Set final failed tests to whatever is left after all retry attempts
    final_failed_tests=("${current_retry_tests[@]}")
    
    # Extract error details for final failed tests
    for failed_test in "${final_failed_tests[@]}"; do
        failed_test_path=""
        for original_file in $original_test_files; do
            if [[ "$(basename $original_file)" == "$failed_test" ]]; then
                failed_test_path="$original_file"
                break
            fi
        done
        
        if [ ! -z "$failed_test_path" ]; then
            extract_error_details "$failed_test_path" "temp_array" "final_error_details" "_retry_attempt_${MAX_RETRY_ATTEMPTS}"
        fi
    done
    
else
    echo -e "\n${GREEN}${BOLD}🎉 NO RETRY NEEDED - ALL TESTS PASSED ON FIRST RUN!${RESET}"
fi

# ===== POST-TEST PROCESSING =====
echo -e "\n${BLUE}${BOLD}🔨 Post-test processing...${RESET}"
# Display final summary
display_final_summary

# Clean up log files only if all tests passed
if [ ${#final_failed_tests[@]} -eq 0 ]; then
    echo -e "\n${BLUE}${BOLD}🧹 All tests passed - cleaning up log files...${RESET}"
    rm -f test_output_*.log*
    echo -e "${GREEN}✨ Log files cleaned up successfully${RESET}"
else
    echo -e "\n${YELLOW}${BOLD}⚠️ ${#final_failed_tests[@]} test(s) failed - keeping log files for debugging:${RESET}"
    echo -e "${CYAN}📁 Failed tests and their log files:${RESET}"
    for failed_test in "${final_failed_tests[@]}"; do
        # Get base name without extension
        test_base=$(basename "$failed_test" .ts)
        echo -e "${YELLOW}   • ${failed_test}${RESET}"
        echo -e "${CYAN}     └─ First run log: test_output_${test_base}_first_run.log${RESET}"
        # List retry logs if they exist
        for attempt in $(seq 1 $MAX_RETRY_ATTEMPTS); do
            retry_log="test_output_${test_base}_retry_attempt_${attempt}.log"
            if [ -f "$retry_log" ]; then
                echo -e "${CYAN}     └─ Retry ${attempt} log: ${retry_log}${RESET}"
            fi
        done
    done
    echo -e "\n${CYAN}💡 Log files location: $(pwd)/test_output_*.log${RESET}"
    echo -e "${CYAN}📝 Use these logs to investigate the failures${RESET}"
fi

# Only upload to S3 and send notification if should_upload_to_s3=true
if [ "$should_upload_to_s3" = true ]; then
    echo -e "\n${BLUE}${BOLD}📊 Generating and uploading report...${RESET}"
    
    if [ -d "allure-results" ] && [ "$(ls -A allure-results)" ]; then
        allure generate allure-results --clean -o allure-report
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Allure report generated successfully${RESET}"
            echo -e "${BLUE}📁 Local report: $(pwd)/allure-report/index.html${RESET}"
            
            if check_aws_setup; then
                echo -e "${BLUE}🚀 Uploading report to S3...${RESET}"
                # After successful S3 upload, send notification
                upload_report_to_s3 "$platform"
            else
                echo -e "${YELLOW}⚠️ Skipping S3 upload due to AWS configuration issues${RESET}"
                echo -e "${YELLOW}⚠️ No Slack notification will be sent (S3 upload required)${RESET}"
            fi
        else
            echo -e "${RED}❌ Failed to generate Allure report${RESET}"
            echo -e "${RED}❌ No Slack notification will be sent${RESET}"
        fi
    else
        echo -e "${YELLOW}⚠️ No test results found to generate report${RESET}"
        echo -e "${YELLOW}⚠️ No Slack notification will be sent${RESET}"
    fi
else
    echo -e "${BLUE}💡 Individual test execution - skipping S3 upload and Slack notification${RESET}"
fi

# Exit with final error count
final_exit_code=${#final_failed_tests[@]}

# Display final completion message
if [ $final_exit_code -eq 0 ]; then
    echo -e "\n${GREEN}${BOLD}✅ All tests completed successfully!${RESET}\n"
    echo -e "${BLUE}🚀 Performance optimizations, retry logic, and crash recovery helped achieve 100% success rate${RESET}"
else
    echo -e "\n${RED}${BOLD}❌ ${final_exit_code} test(s) failed after retry (exit code: $final_exit_code)${RESET}\n"
    echo -e "${YELLOW}💡 These tests may need manual investigation${RESET}"
    echo -e "${YELLOW}📝 Check the log files mentioned above for detailed error information${RESET}\n"
fi

if [ ${#crash_recovery_tests[@]} -gt 0 ]; then
    echo -e "${YELLOW}💥 ${#crash_recovery_tests[@]} test(s) recovered from crashes${RESET}"
fi

# ===== OUTPUT ORGANIZATION (after S3 upload if applicable) =====
echo -e "\n${BLUE}${BOLD}🧹 Starting output organization...${RESET}"

if [ -f "./scripts/organizeOutput.js" ]; then
    echo -e "${YELLOW}📂 Organizing output screenshots into folders...${RESET}"
    node ./scripts/organizeOutput.js
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Output organization completed successfully${RESET}"
    else
        echo -e "${YELLOW}⚠️ Output organization completed with warnings${RESET}"
    fi
else
    echo -e "${YELLOW}⚠️ organizeOutput.js not found - skipping organization${RESET}"
fi

echo -e "${GREEN}${BOLD}✅ All post-test processing completed${RESET}"

echo -e "${BLUE}${BOLD}===========================================${RESET}"
echo -e "${BLUE}${BOLD}Enhanced Platform Tests with Crash Recovery${RESET}"
echo -e "${BLUE}${BOLD}            Complete !!!            ${RESET}"
echo -e "${BLUE}${BOLD}===========================================${RESET}"

exit $final_exit_code
